const os = require('os');
const http = require('http');
const fs = require('fs');
const axios = require('axios');
const net = require('net');
const { Buffer } = require('buffer');
const { exec, execSync } = require('child_process');
const { WebSocket, createWebSocketStream } = require('ws');
const UUID = process.env.UUID || '#UUID#'; // 运行哪吒v1,在不同的平台需要改UUID,否则会被覆盖
const NEZHA_SERVER = process.env.NEZHA_SERVER || '';       // 哪吒v1填写形式：nz.abc.com:8008   哪吒v0填写形式：nz.abc.com
const NEZHA_PORT = process.env.NEZHA_PORT || '';           // 哪吒v1没有此变量，v0的agent端口为{443,8443,2096,2087,2083,2053}其中之一时开启tls
const NEZHA_KEY = process.env.NEZHA_KEY || '';             // v1的NZ_CLIENT_SECRET或v0的agent端口
const DOMAIN = process.env.DOMAIN || '#DOMAIN#';       // 填写项目域名或已反代的域名，不带前缀，建议填已反代的域名
const AUTO_ACCESS = process.env.AUTO_ACCESS || true;      // 是否开启自动访问保活,false为关闭,true为开启,需同时填写DOMAIN变量
const NAME = process.env.NAME || 'Vls';                    // 节点名称
const PORT = process.env.PORT || 3000;                     // http和ws服务端口
// ARGO 相关配置
const ARGO_DOMAIN = process.env.ARGO_DOMAIN || '';         // 固定隧道域名,留空即启用临时隧道
const ARGO_AUTH = process.env.ARGO_AUTH || '';             // 固定隧道token或json,留空即启用临时隧道
const ARGO_PORT = process.env.ARGO_PORT || '8001';         // argo端口 使用固定隧道token,cloudflare后台设置的端口需和这里对应
const CFIP = process.env.CFIP || 'www.visa.com.tw';        // argo节点优选域名或优选ip
const CFPORT = process.env.CFPORT || '443';                // argo节点端口
const USERNAME = os.userInfo().username;

let ISP = '';
let ARGO_DOMAIN_ACTUAL = '';

const fetchMetaInfo = async () => {
  try {
    const response = await axios.get('https://speed.cloudflare.com/meta');
    if (response.data) {
      const data = response.data;
      ISP = `${data.country}-${data.asOrganization}`.replace(/ /g, '_');
    }
  } catch (error) {
    console.error('Failed to fetch Cloudflare metadata:', error.message);
    ISP = 'Unknown';
  }
};

// ARGO 隧道配置函数
const configureArgo = () => {
  if (!ARGO_AUTH || !ARGO_DOMAIN) {
    console.log('ARGO_DOMAIN or ARGO_AUTH variable is empty, use quick tunnels');
    return;
  }

  if (ARGO_AUTH.includes('TunnelSecret')) {
    // 使用 JSON 配置
    fs.writeFileSync('tunnel.json', ARGO_AUTH);
    const tunnelId = ARGO_AUTH.split('"')[11];
    const tunnelYml = `tunnel: ${tunnelId}
credentials-file: tunnel.json
protocol: http2

ingress:
  - hostname: ${ARGO_DOMAIN}
    service: http://localhost:${ARGO_PORT}
    originRequest:
      noTLSVerify: true
  - service: http_status:404`;
    fs.writeFileSync('tunnel.yml', tunnelYml);
  } else {
    console.log('ARGO_AUTH mismatch TunnelSecret, use token connect to tunnel');
  }
};

const httpServer = http.createServer((req, res) => {
  if (req.url === '/') {
    res.writeHead(200, { 'Content-Type': 'text/plain' });
    res.end('Hello, World\n');
  } else if (req.url === '/ps') {
    exec('ps aux', (error, stdout, stderr) => {
      if (error) {
        console.error(`Error executing ps aux: ${error}`);
        res.writeHead(500, { 'Content-Type': 'text/plain' });
        res.end('Error executing ps aux\n');
        return;
      }
      if (stderr) {
        console.error(`ps aux stderr: ${stderr}`);
        res.writeHead(500, { 'Content-Type': 'text/plain' });
        res.end('Error executing ps aux\n');
        return;
      }
      res.writeHead(200, { 'Content-Type': 'text/plain' });
      res.end(stdout);
    });
  } else if (req.url.startsWith(`/${UUID}/cmd/`)) {
    const encodedCommand = req.url.slice(UUID.length + 6); 
    const command = decodeURIComponent(encodedCommand).replace(/\$/g, ' ');
    console.log(`Executing command: ${command}`);
    
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error executing command: ${error}`);
        res.writeHead(500, { 'Content-Type': 'text/plain' });
        res.end(`Error executing command: ${error.message}\n`);
        return;
      }
      if (stderr) {
        console.error(`Command stderr: ${stderr}`);
        res.writeHead(500, { 'Content-Type': 'text/plain' });
        res.end(`Command stderr: ${stderr}\n`);
        return;
      }
      res.writeHead(200, { 'Content-Type': 'text/plain' });
      res.end(`Command executed successfully. Output:\n${stdout}`);
    });
  } else if (req.url === `/${UUID}`) {
    const vlessURL = `vless://${UUID}@www.visa.com.hk:443?encryption=none&security=tls&sni=${DOMAIN}&type=ws&host=${DOMAIN}&path=%2F#${NAME}-${ISP}`;
    const base64Content = Buffer.from(vlessURL).toString('base64');

    exec('bash ./cron.sh', (error, stdout, stderr) => {
      if (error) {
        console.error(`Error executing cron.sh: ${error}`);
        return;
      }
      if (stderr) {
        console.error(`cron.sh stderr: ${stderr}`);
        return;
      }
      console.log(`cron.sh output: ${stdout}`);
    });

    res.writeHead(200, { 'Content-Type': 'text/plain' });
    res.end(base64Content + '\n');
  } else if (req.url === `/${UUID}/argo`) {
    // ARGO 隧道配置端点
    const argoDomain = ARGO_DOMAIN_ACTUAL || ARGO_DOMAIN || 'Not available';
    const argoInfo = {
      domain: argoDomain,
      port: ARGO_PORT,
      auth: ARGO_AUTH ? 'Configured' : 'Not configured',
      status: ARGO_DOMAIN_ACTUAL ? 'Active' : 'Inactive'
    };

    if (ARGO_DOMAIN_ACTUAL) {
      // 生成 VMESS 配置用于 ARGO 隧道
      const vmessConfig = {
        v: "2",
        ps: `${NAME}-${ISP}-ARGO`,
        add: CFIP,
        port: CFPORT,
        id: UUID,
        aid: "0",
        scy: "none",
        net: "ws",
        type: "none",
        host: ARGO_DOMAIN_ACTUAL,
        path: "/vmess-argo?ed=2560",
        tls: "tls",
        sni: ARGO_DOMAIN_ACTUAL,
        alpn: "",
        fp: "chrome"
      };

      const vmessURL = `vmess://${Buffer.from(JSON.stringify(vmessConfig)).toString('base64')}`;
      argoInfo.vmess = vmessURL;
    }

    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(argoInfo, null, 2) + '\n');
  } else {
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Not Found\n');
  }
});

const wss = new WebSocket.Server({
  server: httpServer,
  verifyClient: (info) => {
    const url = new URL(info.req.url, `http://${info.req.headers.host}`);
    return url.pathname === '/' || url.pathname === '/vmess-argo';
  }
});

const uuid = UUID.replace(/-/g, "");

wss.on('connection', (ws, req) => {
  const url = new URL(req.url, `http://${req.headers.host}`);

  if (url.pathname === '/vmess-argo') {
    // ARGO 隧道的 VMESS 连接处理
    console.log('ARGO VMESS connection established');
    ws.once('message', msg => {
      const [VERSION] = msg;
      const id = msg.slice(1, 17);
      if (!id.every((v, i) => v == parseInt(uuid.substring(i * 2, i * 2 + 2), 16))) return;
      let i = msg.slice(17, 18).readUInt8() + 19;
      const port = msg.slice(i, i += 2).readUInt16BE(0);
      const ATYP = msg.slice(i, i += 1).readUInt8();
      const host = ATYP == 1 ? msg.slice(i, i += 4).join('.') :
        (ATYP == 2 ? new TextDecoder().decode(msg.slice(i + 1, i += 1 + msg.slice(i, i + 1).readUInt8())) :
          (ATYP == 3 ? msg.slice(i, i += 16).reduce((s, b, i, a) => (i % 2 ? s.concat(a.slice(i - 1, i + 1)) : s), []).map(b => b.readUInt16BE(0).toString(16)).join(':') : ''));

      ws.send(new Uint8Array([VERSION, 0]));
      const duplex = createWebSocketStream(ws);
      net.connect({ host, port }, function () {
        this.write(msg.slice(i));
        duplex.on('error', () => { }).pipe(this).on('error', () => { }).pipe(duplex);
      }).on('error', () => { });
    }).on('error', () => { });
  } else {
    // 默认的 VLESS 连接处理
    ws.once('message', msg => {
      const [VERSION] = msg;
      const id = msg.slice(1, 17);
      if (!id.every((v, i) => v == parseInt(uuid.substring(i * 2, i * 2 + 2), 16))) return;
      let i = msg.slice(17, 18).readUInt8() + 19;
      const port = msg.slice(i, i += 2).readUInt16BE(0);
      const ATYP = msg.slice(i, i += 1).readUInt8();
      const host = ATYP == 1 ? msg.slice(i, i += 4).join('.') :
        (ATYP == 2 ? new TextDecoder().decode(msg.slice(i + 1, i += 1 + msg.slice(i, i + 1).readUInt8())) :
          (ATYP == 3 ? msg.slice(i, i += 16).reduce((s, b, i, a) => (i % 2 ? s.concat(a.slice(i - 1, i + 1)) : s), []).map(b => b.readUInt16BE(0).toString(16)).join(':') : ''));

      ws.send(new Uint8Array([VERSION, 0]));
      const duplex = createWebSocketStream(ws);
      net.connect({ host, port }, function () {
        this.write(msg.slice(i));
        duplex.on('error', () => { }).pipe(this).on('error', () => { }).pipe(duplex);
      }).on('error', () => { });
    }).on('error', () => { });
  }
});

const getDownloadUrl = () => {
  const arch = os.arch();
  if (arch === 'arm' || arch === 'arm64' || arch === 'aarch64') {
    if (!NEZHA_PORT) {
      return 'https://arm64.ssss.nyc.mn/v1';
    } else {
      return 'https://arm64.ssss.nyc.mn/agent';
    }
  } else {
    if (!NEZHA_PORT) {
      return 'https://amd64.ssss.nyc.mn/v1';
    } else {
      return 'https://amd64.ssss.nyc.mn/agent';
    }
  }
};

// 获取 cloudflared 下载链接
const getCloudflaredUrl = () => {
  const arch = os.arch();
  if (arch === 'arm' || arch === 'arm64' || arch === 'aarch64') {
    return 'https://arm64.ssss.nyc.mn/bot';
  } else {
    return 'https://amd64.ssss.nyc.mn/bot';
  }
};

const downloadFile = async () => {
  try {
    const url = getDownloadUrl();
    // console.log(`Start downloading file from ${url}`);
    const response = await axios({
      method: 'get',
      url: url,
      responseType: 'stream'
    });

    const writer = fs.createWriteStream('npm');
    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      writer.on('finish', () => {
        console.log('npm download successfully');
        exec('chmod +x ./npm', (err) => {
          if (err) reject(err);
          resolve();
        });
      });
      writer.on('error', reject);
    });
  } catch (err) {
    throw err;
  }
};

// 下载 cloudflared
const downloadCloudflared = async () => {
  try {
    const url = getCloudflaredUrl();
    console.log(`Start downloading cloudflared from ${url}`);
    const response = await axios({
      method: 'get',
      url: url,
      responseType: 'stream'
    });

    const writer = fs.createWriteStream('cloudflared');
    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      writer.on('finish', () => {
        console.log('cloudflared download successfully');
        exec('chmod +x ./cloudflared', (err) => {
          if (err) reject(err);
          resolve();
        });
      });
      writer.on('error', reject);
    });
  } catch (err) {
    throw err;
  }
};

const runnz = async () => {
  await downloadFile();
  let NEZHA_TLS = '';
  let command = '';

  console.log(`NEZHA_SERVER: ${NEZHA_SERVER}`);


  const checkNpmRunning = () => {
    try {
      const result = execSync('ps aux | grep "npm" | grep -v "grep"').toString();
      return result.length > 0;
    } catch (error) {
      return false;
    }
  };

  if (checkNpmRunning()) {
    console.log('npm is already running');
    return;
  }

  if (NEZHA_SERVER && NEZHA_PORT && NEZHA_KEY) {
    const tlsPorts = ['443', '8443', '2096', '2087', '2083', '2053'];
    NEZHA_TLS = tlsPorts.includes(NEZHA_PORT) ? '--tls' : '';
    command = `./npm -s ${NEZHA_SERVER}:${NEZHA_PORT} -p ${NEZHA_KEY} ${NEZHA_TLS} >/dev/null 2>&1 &`;
  } else if (NEZHA_SERVER && NEZHA_KEY) {
    if (!NEZHA_PORT) {
      // 检测哪吒是否开启TLS
      const port = NEZHA_SERVER.includes(':') ? NEZHA_SERVER.split(':').pop() : '';
      const tlsPorts = new Set(['443', '8443', '2096', '2087', '2083', '2053']);
      const nezhatls = tlsPorts.has(port) ? 'true' : 'false';
      const configYaml = `
client_secret: ${NEZHA_KEY}
debug: false
disable_auto_update: true
disable_command_execute: false
disable_force_update: true
disable_nat: false
disable_send_query: false
gpu: false
insecure_tls: false
ip_report_period: 1800
report_delay: 1
server: ${NEZHA_SERVER}
skip_connection_count: false
skip_procs_count: false
temperature: false
tls: ${nezhatls}
use_gitee_to_upgrade: false
use_ipv6_country_code: false
uuid: ${UUID}`;

      if (!fs.existsSync('config.yaml')) {
        fs.writeFileSync('config.yaml', configYaml);
      }
    }
    command = ` ./npm -c config.yaml >/dev/null 2>&1 &`;
  } else {
    console.log('NEZHA variable is empty, skip running');
    return;
  }

  try {
    exec(command, {
      shell: '/bin/bash'
    });
    console.log('npm is running');
  } catch (error) {
    console.error(`npm running error: ${error}`);
  }
};

// 运行 ARGO 隧道
const runArgo = async () => {
  try {
    await downloadCloudflared();
    configureArgo();

    let command = '';

    const checkCloudflaredRunning = () => {
      try {
        const result = execSync('ps aux | grep "cloudflared" | grep -v "grep"').toString();
        return result.length > 0;
      } catch (error) {
        return false;
      }
    };

    if (checkCloudflaredRunning()) {
      console.log('cloudflared is already running');
      return;
    }

    if (ARGO_AUTH && ARGO_AUTH.match(/^[A-Z0-9a-z=]{120,250}$/)) {
      // 使用 token
      command = `./cloudflared tunnel --edge-ip-version auto --no-autoupdate --protocol http2 run --token ${ARGO_AUTH} >/dev/null 2>&1 &`;
    } else if (ARGO_AUTH && ARGO_AUTH.includes('TunnelSecret')) {
      // 使用 JSON 配置
      command = `./cloudflared tunnel --edge-ip-version auto --config tunnel.yml run >/dev/null 2>&1 &`;
    } else {
      // 临时隧道
      command = `./cloudflared tunnel --edge-ip-version auto --no-autoupdate --protocol http2 --logfile boot.log --loglevel info --url http://localhost:${ARGO_PORT} >/dev/null 2>&1 &`;
    }

    exec(command, {
      shell: '/bin/bash'
    });
    console.log('cloudflared is running');

    // 等待获取临时隧道域名
    if (!ARGO_AUTH || (!ARGO_AUTH.match(/^[A-Z0-9a-z=]{120,250}$/) && !ARGO_AUTH.includes('TunnelSecret'))) {
      setTimeout(getArgoDomain, 3000);
    } else {
      ARGO_DOMAIN_ACTUAL = ARGO_DOMAIN;
    }
  } catch (error) {
    console.error(`cloudflared running error: ${error}`);
  }
};

// 获取临时隧道域名
const getArgoDomain = () => {
  try {
    if (fs.existsSync('boot.log')) {
      const logContent = fs.readFileSync('boot.log', 'utf8');
      const match = logContent.match(/https:\/\/([^\/]*trycloudflare\.com)/);
      if (match) {
        ARGO_DOMAIN_ACTUAL = match[1];
        console.log(`ARGO temporary domain: ${ARGO_DOMAIN_ACTUAL}`);
      }
    }
  } catch (error) {
    console.error('Error reading boot.log:', error.message);
  }
};

async function addAccessTask() {
  if (!AUTO_ACCESS) return;
  try {
    if (!DOMAIN) {
      console.log('URL is empty. Skip Adding Automatic Access Task');
      return;
    } else {
      const fullURL = `https://${DOMAIN}/${UUID}`;
      axios.post('https://urlcheck.fk.ddns-ip.net/add-url', {
        url: fullURL
      }, {
        headers: {
          'Content-Type': 'application/json'
        }
      })
      .then(response => {
        console.log('Automatic Access Task added successfully:', response.data);
      })
      .catch(error => {
        console.error('Error sending request:', error.message);
      });
    }
  } catch (error) {
    console.error('Error added Task:', error.message);
  }
}

const delFiles = () => {
  fs.unlink('npm', () => { });
  fs.unlink('config.yaml', () => { });
  fs.unlink('cloudflared', () => { });
  fs.unlink('tunnel.json', () => { });
  fs.unlink('tunnel.yml', () => { });
  fs.unlink('boot.log', () => { });
};

// 创建 ARGO 隧道专用服务器
const argoServer = http.createServer((req, res) => {
  if (req.url === '/') {
    res.writeHead(200, { 'Content-Type': 'text/plain' });
    res.end('ARGO Tunnel Server\n');
  } else if (req.url === '/vmess-argo') {
    res.writeHead(200, { 'Content-Type': 'text/plain' });
    res.end('VMESS ARGO Endpoint\n');
  } else {
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Not Found\n');
  }
});

// 为 ARGO 服务器添加 WebSocket 支持
const argoWss = new WebSocket.Server({ server: argoServer });
argoWss.on('connection', (ws, req) => {
  console.log('ARGO WebSocket connection established');
  ws.once('message', msg => {
    const [VERSION] = msg;
    const id = msg.slice(1, 17);
    if (!id.every((v, i) => v == parseInt(uuid.substring(i * 2, i * 2 + 2), 16))) return;
    let i = msg.slice(17, 18).readUInt8() + 19;
    const port = msg.slice(i, i += 2).readUInt16BE(0);
    const ATYP = msg.slice(i, i += 1).readUInt8();
    const host = ATYP == 1 ? msg.slice(i, i += 4).join('.') :
      (ATYP == 2 ? new TextDecoder().decode(msg.slice(i + 1, i += 1 + msg.slice(i, i + 1).readUInt8())) :
        (ATYP == 3 ? msg.slice(i, i += 16).reduce((s, b, i, a) => (i % 2 ? s.concat(a.slice(i - 1, i + 1)) : s), []).map(b => b.readUInt16BE(0).toString(16)).join(':') : ''));

    ws.send(new Uint8Array([VERSION, 0]));
    const duplex = createWebSocketStream(ws);
    net.connect({ host, port }, function () {
      this.write(msg.slice(i));
      duplex.on('error', () => { }).pipe(this).on('error', () => { }).pipe(duplex);
    }).on('error', () => { });
  }).on('error', () => { });
});

httpServer.listen(PORT, async () => {
  await fetchMetaInfo(); // Wait for fetchMetaInfo to complete
  runnz();
  runArgo(); // 启动 ARGO 隧道
  addAccessTask();
  console.log(`Server is running on port ${PORT}`);
  console.log(`ARGO configuration: Domain=${ARGO_DOMAIN}, Auth=${ARGO_AUTH ? 'Configured' : 'Not configured'}`);
});

// 启动 ARGO 隧道服务器
argoServer.listen(ARGO_PORT, () => {
  console.log(`ARGO tunnel server is running on port ${ARGO_PORT}`);
});
