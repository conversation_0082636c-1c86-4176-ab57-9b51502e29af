#!/bin/bash  
export UUID=${UUID:-'bc97f674-c578-4940-9234-0a1da46041b9'} # 哪吒v1,在不同的平台部署需要改UUID，否则会覆盖
export NEZHA_SERVER=${NEZHA_SERVER:-''}       # v1哪吒填写形式：nezha.abc.com:8008,v0哪吒填写形式：nezha.abc.com
export NEZHA_PORT=${NEZHA_PORT:-''}           # v1哪吒不要填写这个,v0哪吒agent端口为{443,8443,2053,2083,2087,2096}其中之一时自动开启tls
export NEZHA_KEY=${NEZHA_KEY:-''}             # v1的NZ_CLIENT_SECRET或v0的agent密钥
export ARGO_DOMAIN=${ARGO_DOMAIN:-''}         # 固定隧道域名,留空即启用临时隧道
export ARGO_AUTH=${ARGO_AUTH:-''}             # 固定隧道token或json,留空即启用临时隧道
export CFIP=${CFIP:-'www.visa.com.tw'}        # argo节点优选域名或优选ip
export CFPORT=${CFPORT:-'443'}                # argo节点端口 
export NAME=${NAME:-'Vls'}                    # 节点名称  
export FILE_PATH=${FILE_PATH:-'./.npm'}       # sub 路径  
export ARGO_PORT=${ARGO_PORT:-'8001'}         # argo端口 使用固定隧道token,cloudflare后台设置的端口需和这里对应
export TUIC_PORT=${TUIC_PORT:-'40000'}        # Tuic 端口，支持多端口玩具可填写，否则不动
export HY2_PORT=${HY2_PORT:-'50000'}          # Hy2 端口，支持多端口玩具可填写，否则不动
export REALITY_PORT=${REALITY_PORT:-'60000'}  # reality 端口,支持多端口玩具可填写，否则不动   
export CHAT_ID=${CHAT_ID:-''}                 # TG chat_id，可在https://t.me/laowang_serv00_bot 获取
export BOT_TOKEN=${BOT_TOKEN:-''}             # TG bot_token, 使用自己的bot需要填写,使用上方的bot不用填写,不会给别人发送
export UPLOAD_URL=${UPLOAD_URL:-''}  # 订阅自动上传地址,没有可不填,需要填部署Merge-sub项目后的首页地址,例如：https://merge.serv00.net

echo "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" | base64 -d | bash
